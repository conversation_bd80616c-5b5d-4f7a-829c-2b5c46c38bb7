<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ConfirmPart.aspx.cs" Inherits="WebApplication1.Flow.ConfirmPart" %>

<!DOCTYPE html>
<head runat="server">
<meta charset="utf-8">
    <title>共用料确认</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        /* ===== 页面基础样式重置 ===== */
        .layuimini-container {
            padding-bottom: 80px !important;
        }

        /* ===== 卡片容器样式 ===== */
        .cards-container {
            padding: 20px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .bom-card {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .bom-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #009688;
        }

        .bom-card.selected {
            border-color: #009688;
            background-color: #f0f9ff;
        }

        /* ===== 卡片头部 ===== */
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
            border-radius: 8px 8px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-checkbox {
            margin-right: 15px;
        }

        .card-title {
            flex: 1;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
        }

        .card-title .part-number {
            color: #009688;
            margin-right: 10px;
        }

        .card-title .description {
            color: #666;
            font-weight: normal;
            font-size: 14px;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .card-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .expand-btn {
            background: none;
            border: none;
            color: #009688;
            cursor: pointer;
            font-size: 18px;
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .expand-btn:hover {
            background-color: #e0f2f1;
        }

        /* ===== 卡片内容 ===== */
        .card-content {
            padding: 20px;
        }

        .card-content.collapsed {
            display: none;
        }

        /* ===== 主要信息区域 ===== */
        .main-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #009688;
        }

        .info-group h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: bold;
        }

        .info-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .info-label {
            min-width: 80px;
            color: #666;
            font-size: 12px;
        }

        .info-value {
            color: #333;
            font-size: 13px;
            flex: 1;
        }

        /* ===== 物料组和匹配料号区域 ===== */
        .control-group {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .control-group h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 14px;
            font-weight: bold;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 8px;
        }

        .control-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .control-label {
            min-width: 100px;
            color: #666;
            font-size: 13px;
            margin-right: 10px;
        }

        .control-input {
            flex: 1;
            margin-right: 15px;
        }

        /* ===== 物料组Select-Input样式 ===== */
        .material-group-container {
            width: 100%;
            position: relative;
        }

        .material-group-container .select-input-input {
            width: 100%;
            height: 36px;
            line-height: 36px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 0 25px 0 12px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .material-group-container .select-input-input:focus {
            border-color: #009688;
            box-shadow: 0 0 0 2px rgba(0, 150, 136, 0.1);
        }

        .material-group-container .select-input-body {
            z-index: 999999;
        }

        /* ===== 匹配料号下拉框样式 ===== */
        .match-select-wrapper {
            position: relative;
            width: 100%;
        }

        .match-select-wrapper .layui-form-select {
            width: 100%;
        }

        .match-select-wrapper .layui-form-select .layui-input {
            height: 36px;
            line-height: 36px;
            font-size: 13px;
            border-radius: 4px;
        }

        .match-select-wrapper .layui-form-select dl {
            min-width: 350px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 999999;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .match-select-wrapper .layui-form-select dl dd {
            padding: 10px 12px;
            font-size: 12px;
            border-bottom: 1px solid #f5f5f5;
            white-space: nowrap;
            transition: background-color 0.2s;
        }

        .match-select-wrapper .layui-form-select dl dd:hover {
            background-color: #f0f9ff;
        }

        /* ===== 匹配料号选项标识样式 ===== */
        .multiple-indicator,
        .single-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 10px;
            padding: 3px 8px;
            border-radius: 12px;
            color: white;
            font-weight: bold;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .multiple-indicator {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }

        .single-indicator {
            background: linear-gradient(45deg, #4caf50, #388e3c);
        }

        /* ===== 详细信息区域 ===== */
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #009688;
        }

        .detail-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 13px;
            color: #333;
            font-weight: 500;
            word-break: break-all;
        }

        /* ===== 批量操作区域 ===== */
        .batch-actions {
            background: #f0f9ff;
            border: 1px solid #b3e5fc;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }

        .batch-info {
            color: #0277bd;
            font-size: 14px;
            font-weight: 500;
        }

        .batch-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .select-all-btn {
            background: #009688;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .select-all-btn:hover {
            background: #00796b;
        }

        /* ===== 固定底部按钮样式 ===== */
        .fixed-bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #e6e6e6;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .action-buttons {
            text-align: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .action-buttons .layui-btn {
            padding: 12px 30px;
            font-size: 14px;
            border-radius: 4px;
            min-width: 120px;
            height: auto;
            line-height: 1.4;
        }

        .action-buttons .layui-btn .layui-icon {
            margin-right: 5px;
            vertical-align: middle;
        }

        /* ===== 快捷操作区域样式 ===== */
        .layui-form-item {
            margin-bottom: 15px;
        }

        .layui-form-label {
            width: 100px;
            text-align: right;
            padding: 9px 15px;
        }

        .layui-input-inline {
            width: 200px;
            margin-right: 10px;
        }

        .layui-btn {
            margin-left: 10px;
        }

        /* ===== 响应式调整 ===== */
        @media (max-width: 768px) {
            .main-info {
                grid-template-columns: 1fr;
            }

            .details-grid {
                grid-template-columns: 1fr;
            }

            .batch-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .batch-controls {
                justify-content: center;
            }

            .card-header {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .card-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .layui-form-label {
                width: 80px;
                padding: 9px 10px;
            }

            .layui-input-inline {
                width: 150px;
            }

            .action-buttons .layui-btn {
                padding: 10px 20px;
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .cards-container {
                padding: 10px;
            }

            .bom-card {
                margin-bottom: 10px;
            }

            .card-header {
                padding: 10px 15px;
            }

            .card-content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
                <legend>快捷操作</legend>
                <div style="margin: 10px 10px 10px 10px">
                    <form class="layui-form layui-form-pane" id="rfqForm">

                        <div class="layui-form-item">
                            <label class="layui-form-label">物料组名</label>
                            <div class="layui-input-inline">
                                <select id="MaterialType" lay-filter="materialTypeSelect">
                                    <option value="">请选择需要填充的物料组</option>
                                </select>
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" id="CopyGroup">一键填充物料组</button>
                            <button type="button" class="layui-btn layui-btn-primary" id="RefreshGroups">刷新物料组</button>
                        </div>
                    </form>
                </div>
            </fieldset>

        <!-- 批量操作区域 -->
        <div class="batch-actions" id="batchActions" style="display: none;">
            <div class="batch-info">
                <span id="selectedCount">已选择 0 项</span>
            </div>
            <div class="batch-controls">
                <button type="button" class="select-all-btn" id="selectAllBtn">全选</button>
                <button type="button" class="select-all-btn" id="clearAllBtn" style="background: #f56c6c;">清空选择</button>
            </div>
        </div>

        <!-- 卡片容器 -->
        <div class="cards-container" id="cardsContainer">
            <!-- 卡片将通过JavaScript动态生成 -->
        </div>



    </div>

        <!-- 固定在底部的操作按钮区域 -->
        <div class="fixed-bottom-actions">
            <div class="action-buttons">
                <button type="button" id="nextbtn" class="layui-btn layui-btn-normal">
                    <i class="layui-icon layui-icon-next"></i> 下一步
                </button>
            </div>
        </div>
</div>
</body>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="../js/navigate.js" charset="utf-8"></script>
<script src="../js/lay-module/select-input2.x-1.0.2/dist/selectInput.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'upload', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form;

        function getQueryParam(name) {
            // 保留原有的参数获取逻辑
            var hash = window.location.hash;
            var hashParams = hash.substring(hash.indexOf('?') + 1);
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = hashParams.match(reg);

            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            var queryParams = window.location.search.substr(1);
            r = queryParams.match(reg);
            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            return null;
        }
        // 从URL获取RFQNo
        var RFQNo = getQueryParam('RFQNo');
        console.log(RFQNo);

        // 全局数据存储
        var optionsData = {};
        var materialGroupsData = [];
        var bomData = [];
        var columnsData = [];
        var selectedItems = new Set();
        var materialGroupInstances = {};

        // 获取物料组数据
        function loadMaterialGroups() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '../ashx/ConfirmControl.ashx?action=GetMaterialGroups',
                    type: 'GET',
                    success: function (response) {
                        var data = JSON.parse(response);
                        if (data.code === 0) {
                            materialGroupsData = data.data.map(item => item.GroupName);
                            resolve(materialGroupsData);
                        } else {
                            layer.msg('获取物料组数据失败: ' + data.msg);
                            reject(data.msg);
                        }
                    },
                    error: function () {
                        layer.msg('获取物料组数据失败');
                        reject('网络错误');
                    }
                });
            });
        }



        function loadColumnAndBomData() {
            var index = layer.load(1, { shade: [0.5, '#fff'] });

            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=QueryColumn&RFQNo=' + RFQNo,
                type: 'POST',
                success: function (response) {
                    console.log('Column response:', response);
                    var result = JSON.parse(response);
                    if (result.result == 'success') {
                        var fielddata = result.col;
                        var modeldata = result.models;

                        // 构建列信息
                        columnsData = [];

                        // 基础列
                        columnsData.push({ field: 'Id', title: 'Id', hide: true });
                        columnsData.push({ field: 'MATERIAL_GROUP', title: '物料组' });
                        columnsData.push({ field: 'MATCH_PARTNUMBER', title: '匹配料号' });
                        columnsData.push({ field: 'MATCH_CUST', title: '客户料号' });
                        columnsData.push({ field: 'MATCH_MANUFACTURER', title: '制造商' });
                        columnsData.push({ field: 'MATCH_MPN', title: 'MPN' });

                        // 动态字段列
                        if (fielddata && fielddata.length > 0) {
                            fielddata.forEach(function (fd) {
                                columnsData.push({
                                    field: fd.DatabaseField,
                                    title: fd.DatabaseField
                                });
                            });
                        }

                        // 模型列
                        if (modeldata && modeldata.length > 0) {
                            modeldata.forEach(function (model) {
                                columnsData.push({
                                    field: model,
                                    title: model
                                });
                            });
                        }

                        // 加载BOM数据
                        $.ajax({
                            url: '../ashx/ConfirmControl.ashx?action=QueryBom&RFQNo=' + RFQNo,
                            type: 'POST',
                            success: function (bomResponse) {
                                console.log('BOM response:', bomResponse);
                                bomData = JSON.parse(bomResponse);
                                renderCards();
                                layer.close(index);
                            },
                            error: function (data) {
                                layer.alert('加载BOM数据失败');
                                layer.close(index);
                            }
                        });

                    } else {
                        layer.alert("RFQNo为空,请先导入项目信息");
                        layer.close(index);
                    }
                },
                error: function (data) {
                    layer.alert('加载列数据失败');
                    layer.close(index);
                }
            });
        }
        // 渲染卡片
        function renderCards() {
            var container = $('#cardsContainer');
            container.empty();

            if (!bomData || bomData.length === 0) {
                container.html('<div style="text-align: center; padding: 50px; color: #999;">暂无数据</div>');
                return;
            }

            bomData.forEach(function(item, index) {
                var cardHtml = createCardHtml(item, index);
                container.append(cardHtml);
            });

            // 初始化卡片功能
            initializeCardFunctions();

            // 默认全选
            setTimeout(function() {
                selectAllItems();
            }, 500);
        }

        // 创建单个卡片HTML
        function createCardHtml(item, index) {
            var cardId = 'card-' + item.Id;
            var isSelected = selectedItems.has(item.Id);

            var html = '<div class="bom-card' + (isSelected ? ' selected' : '') + '" data-id="' + item.Id + '" id="' + cardId + '">';

            // 卡片头部
            html += '<div class="card-header">';
            html += '<div class="card-checkbox">';
            html += '<input type="checkbox" lay-skin="primary" ' + (isSelected ? 'checked' : '') + ' data-id="' + item.Id + '">';
            html += '</div>';
            html += '<div class="card-title">';
            html += '<span class="part-number">' + (item.MATCH_PARTNUMBER || '未匹配') + '</span>';
            html += '<span class="description">' + (item.BOM_DESC || '') + '</span>';
            html += '</div>';
            html += '<div class="card-actions">';
            html += '<button class="expand-btn" data-id="' + item.Id + '">▼</button>';
            html += '</div>';
            html += '</div>';

            // 卡片内容
            html += '<div class="card-content" id="content-' + item.Id + '">';

            // 主要信息区域
            html += '<div class="main-info">';

            // 物料组和匹配信息
            html += '<div class="info-group">';
            html += '<h4>物料组和匹配信息</h4>';
            html += '<div class="control-group">';
            html += '<div class="control-row">';
            html += '<span class="control-label">物料组:</span>';
            html += '<div class="control-input">';
            html += '<div class="material-group-container">';
            html += '<div id="material-group-' + item.Id + '" data-id="' + item.Id + '"></div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '<div class="control-row">';
            html += '<span class="control-label">匹配料号:</span>';
            html += '<div class="control-input">';
            html += '<div class="match-select-wrapper" data-id="' + item.Id + '">';
            html += '<select lay-filter="matchSelect" class="match-select layui-select" lay-search data-id="' + item.Id + '">';
            html += '<option value="' + (item.MATCH_PARTNUMBER || '') + '">' + (item.MATCH_PARTNUMBER || '请选择匹配料号') + '</option>';
            html += '</select>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';

            // 匹配详情
            html += '<div class="info-group">';
            html += '<h4>匹配详情</h4>';
            html += '<div class="info-item">';
            html += '<span class="info-label">客户料号:</span>';
            html += '<span class="info-value" data-field="MATCH_CUST">' + (item.MATCH_CUST || '') + '</span>';
            html += '</div>';
            html += '<div class="info-item">';
            html += '<span class="info-label">制造商:</span>';
            html += '<span class="info-value" data-field="MATCH_MANUFACTURER">' + (item.MATCH_MANUFACTURER || '') + '</span>';
            html += '</div>';
            html += '<div class="info-item">';
            html += '<span class="info-label">MPN:</span>';
            html += '<span class="info-value" data-field="MATCH_MPN">' + (item.MATCH_MPN || '') + '</span>';
            html += '</div>';
            html += '</div>';

            html += '</div>';

            // 详细信息网格
            html += '<div class="details-grid">';
            columnsData.forEach(function(col) {
                if (col.field !== 'Id' && col.field !== 'MATERIAL_GROUP' &&
                    !col.field.startsWith('MATCH_') && item[col.field]) {
                    html += '<div class="detail-item">';
                    html += '<div class="detail-label">' + col.title + '</div>';
                    html += '<div class="detail-value">' + (item[col.field] || '') + '</div>';
                    html += '</div>';
                }
            });
            html += '</div>';

            html += '</div>';
            html += '</div>';

            return html;
        }

        // 初始化快捷操作区域的物料组选择
        function initializeQuickFillSelect() {
            loadMaterialGroups().then(function(groups) {
                var $select = $('#MaterialType');
                $select.empty().append('<option value="">请选择需要填充的物料组</option>');
                groups.forEach(function(group) {
                    $select.append('<option value="' + group + '">' + group + '</option>');
                });
                form.render('select');
            });
        }

        // 初始化卡片功能
        function initializeCardFunctions() {
            // 初始化复选框
            form.render('checkbox');

            // 初始化物料组输入框
            initializeMaterialGroupInputs();

            // 初始化匹配料号选择框
            form.render('select');
            loadMatchPartnumberOptions();

            // 绑定事件
            bindCardEvents();

            // 更新批量操作区域
            updateBatchActions();
        }

        // 绑定卡片事件
        function bindCardEvents() {
            // 复选框变化事件
            $(document).off('click', '.bom-card input[type="checkbox"]').on('click', '.bom-card input[type="checkbox"]', function(e) {
                e.stopPropagation();
                var itemId = $(this).data('id');
                var isChecked = $(this).is(':checked');

                if (isChecked) {
                    selectedItems.add(itemId);
                    $(this).closest('.bom-card').addClass('selected');
                } else {
                    selectedItems.delete(itemId);
                    $(this).closest('.bom-card').removeClass('selected');
                }

                updateBatchActions();
            });

            // 展开/折叠按钮事件
            $(document).off('click', '.expand-btn').on('click', '.expand-btn', function(e) {
                e.stopPropagation();
                var itemId = $(this).data('id');
                var content = $('#content-' + itemId);
                var btn = $(this);

                if (content.hasClass('collapsed')) {
                    content.removeClass('collapsed');
                    btn.text('▼');
                } else {
                    content.addClass('collapsed');
                    btn.text('▶');
                }
            });

            // 卡片点击事件（切换选择状态）
            $(document).off('click', '.bom-card').on('click', '.bom-card', function(e) {
                if ($(e.target).is('input, select, button, .select-input-input, .layui-form-select')) {
                    return;
                }

                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.trigger('click');
            });
        }

        // 更新批量操作区域
        function updateBatchActions() {
            var selectedCount = selectedItems.size;
            var totalCount = bomData.length;

            $('#selectedCount').text('已选择 ' + selectedCount + ' / ' + totalCount + ' 项');

            if (selectedCount > 0) {
                $('#batchActions').show();
            } else {
                $('#batchActions').hide();
            }

            // 更新全选按钮状态
            if (selectedCount === totalCount) {
                $('#selectAllBtn').text('取消全选');
            } else {
                $('#selectAllBtn').text('全选');
            }
        }

        // 全选/取消全选
        function selectAllItems() {
            var allSelected = selectedItems.size === bomData.length;

            if (allSelected) {
                // 取消全选
                selectedItems.clear();
                $('.bom-card input[type="checkbox"]').prop('checked', false);
                $('.bom-card').removeClass('selected');
            } else {
                // 全选
                bomData.forEach(function(item) {
                    selectedItems.add(item.Id);
                });
                $('.bom-card input[type="checkbox"]').prop('checked', true);
                $('.bom-card').addClass('selected');
            }

            form.render('checkbox');
            updateBatchActions();
        }

        // 清空选择
        function clearAllItems() {
            selectedItems.clear();
            $('.bom-card input[type="checkbox"]').prop('checked', false);
            $('.bom-card').removeClass('selected');
            form.render('checkbox');
            updateBatchActions();
        }

        // 重写物料组输入框初始化函数
        function initializeMaterialGroupInputs() {
            $('.material-group-container [id^="material-group-"]').each(function() {
                var $container = $(this);
                var elemId = $container.attr('id');
                var dataId = $container.data('id');

                // 如果实例已存在，先销毁
                if (materialGroupInstances[elemId]) {
                    try {
                        materialGroupInstances[elemId].emptyValue();
                    } catch(e) {}
                }

                // 获取当前值
                var currentValue = '';
                var itemData = bomData.find(function(item) {
                    return item.Id === dataId;
                });
                if (itemData && itemData.MATERIAL_GROUP) {
                    currentValue = itemData.MATERIAL_GROUP;
                }

                // 检查selectInput是否可用
                if (typeof window.selectInput === 'undefined') {
                    console.error('selectInput未加载，无法创建物料组实例');
                    return;
                }

                // 检查目标元素是否存在
                var targetElem = document.getElementById(elemId);
                if (!targetElem) {
                    console.error('目标元素不存在:', elemId);
                    return;
                }

                // 创建select-input实例
                var instance = window.selectInput.render({
                    elem: '#' + elemId,
                    data: materialGroupsData.map(function(group) {
                        return {name: group, value: group};
                    }),
                    initValue: currentValue,
                    placeholder: '请输入或选择物料组',
                    hasSelectIcon: true,
                    localSearch: true,
                    ignoreCase: true,
                    clickClose: true,
                    height: '150px',
                    invisibleMode: true,
                    onBlur: function(value) {
                        var result = instance.getValue();
                        if (result.value && !result.isSelect && !materialGroupsData.includes(result.value)) {
                            instance.emptyValue();
                            layer.msg('请输入有效的物料组值，或从下拉列表中选择', {icon: 2});
                        }
                    }
                });

                materialGroupInstances[elemId] = instance;
            });
        }


        // 缓存匹配料号数据，避免重复加载
        var matchDataCache = {};
        var isLoadingMatchData = false;
        var hasInitializedMatchData = false;

        function loadMatchPartnumberOptions() {
            console.log('loadMatchPartnumberOptions被调用，hasInitializedMatchData:', hasInitializedMatchData);

            // 如果已经初始化过且有缓存数据，直接渲染
            if (hasInitializedMatchData && Object.keys(matchDataCache).length > 0) {
                console.log('使用缓存数据渲染匹配料号选项');
                renderMatchSelects();
                return;
            }

            if (isLoadingMatchData) {
                console.log('正在加载中，跳过重复请求');
                return;
            }

            isLoadingMatchData = true;
            console.log('开始加载匹配料号数据...');

            // 收集所有需要加载的ID
            var idsToLoad = [];
            $('.match-select').each(function () {
                var rowId = $(this).data('id');
                if (!matchDataCache[rowId]) {
                    idsToLoad.push(rowId);
                }
            });

            console.log('需要加载的ID数量:', idsToLoad.length);

            if (idsToLoad.length === 0) {
                console.log('所有数据已缓存，直接渲染');
                renderMatchSelects();
                isLoadingMatchData = false;
                hasInitializedMatchData = true;
                return;
            }

            // 批量加载数据
            var loadPromises = idsToLoad.map(function(rowId) {
                return new Promise(function(resolve, reject) {
                    $.ajax({
                        url: '../ashx/ConfirmControl.ashx?action=GetMatchOptions',
                        type: 'POST',
                        data: { id: rowId },
                        success: function (response) {
                            try {
                                var data = JSON.parse(response);
                                matchDataCache[rowId] = data;
                                optionsData[rowId] = data;
                                console.log('成功加载ID:', rowId, '数据量:', data.length);
                                resolve({rowId: rowId, data: data});
                            } catch(e) {
                                console.error('解析数据失败:', e);
                                reject(e);
                            }
                        },
                        error: function () {
                            console.error('加载ID失败:', rowId);
                            reject(new Error('加载失败'));
                        }
                    });
                });
            });

            // 等待所有数据加载完成
            Promise.all(loadPromises).then(function(results) {
                console.log('所有匹配料号数据加载完成');
                renderMatchSelects();
                isLoadingMatchData = false;
                hasInitializedMatchData = true;
            }).catch(function(error) {
                console.error('批量加载匹配料号失败:', error);
                isLoadingMatchData = false;
            });
        }

        function renderMatchSelects() {
            $('.match-select').each(function () {
                var select = $(this);
                var rowId = select.data('id');
                var currentValue = select.find('option:first').text();
                var wrapper = select.closest('.match-select-wrapper');
                var data = matchDataCache[rowId] || [];

                // 如果有数据且当前值为空或默认值，则自动选择第一项
                if (data.length > 0 && (!currentValue || currentValue === '请选择匹配料号' || currentValue === '加载中...' || currentValue === '请选择')) {
                    var firstItem = data[0];
                    currentValue = firstItem.MATCH_PARTNUMBER;

                    // 更新bomData中的数据
                    var itemData = bomData.find(function(item) {
                        return item.Id === rowId;
                    });
                    if (itemData) {
                        Object.assign(itemData, {
                            MATCH_PARTNUMBER: firstItem.MATCH_PARTNUMBER,
                            MATCH_CUST: firstItem.MATCH_CUST,
                            MATCH_MANUFACTURER: firstItem.MATCH_MANUFACTURER,
                            MATCH_MPN: firstItem.MATCH_MPN
                        });

                        // 更新卡片中的显示
                        var $card = select.closest('.bom-card');
                        $card.find('.part-number').text(firstItem.MATCH_PARTNUMBER);
                        $card.find('[data-field="MATCH_CUST"]').text(firstItem.MATCH_CUST === 'NA' ? '' : firstItem.MATCH_CUST);
                        $card.find('[data-field="MATCH_MANUFACTURER"]').text(firstItem.MATCH_MANUFACTURER === 'NA' ? '' : firstItem.MATCH_MANUFACTURER);
                        $card.find('[data-field="MATCH_MPN"]').text(firstItem.MATCH_MPN === 'NA' ? '' : firstItem.MATCH_MPN);
                    }
                }

                select.empty().append(
                    '<option value="' + currentValue + '">' +
                    (currentValue || '请选择') +
                    '</option>'
                );

                data.forEach(function (item) {
                    if (item.MATCH_PARTNUMBER !== currentValue) {
                        // 创建更清晰的显示格式
                        var displayText = item.MATCH_PARTNUMBER || 'NA';
                        var detailInfo = [];

                        if (item.MATCH_CUST && item.MATCH_CUST !== 'NA') {
                            detailInfo.push('客户: ' + item.MATCH_CUST);
                        }
                        if (item.MATCH_MANUFACTURER && item.MATCH_MANUFACTURER !== 'NA') {
                            detailInfo.push('制造商: ' + item.MATCH_MANUFACTURER);
                        }
                        if (item.MATCH_MPN && item.MATCH_MPN !== 'NA') {
                            detailInfo.push('MPN: ' + item.MATCH_MPN);
                        }

                        if (detailInfo.length > 0) {
                            displayText += ' (' + detailInfo.join(', ') + ')';
                        }

                        select.append(
                            $('<option>', {
                                value: item.MATCH_PARTNUMBER,
                                text: displayText,
                                'data-cust': item.MATCH_CUST || '',
                                'data-manufacturer': item.MATCH_MANUFACTURER || '',
                                'data-mpn': item.MATCH_MPN || ''
                            })
                        );
                    }
                });

                // 根据选项数量决定是否显示下拉箭头和添加标识
                if (data.length > 1) {
                    wrapper.addClass('has-multiple');
                    if (!wrapper.find('.multiple-indicator').length) {
                        wrapper.append('<span class="multiple-indicator">多选项</span>');
                    }
                } else {
                    wrapper.removeClass('has-multiple');
                    if (!wrapper.find('.single-indicator').length) {
                        wrapper.append('<span class="single-indicator">单选项</span>');
                    }
                    wrapper.find('.multiple-indicator').remove();
                }

                form.render('select');
            });
        }

        form.on('select(matchSelect)', function (data) {
            var $select = $(data.elem);
            var rowId = $select.data('id');
            var $option = $select.find('option:selected');
            var $card = $select.closest('.bom-card');

            // 更新数据
            var rowData = {
                MATCH_PARTNUMBER: data.value,
                MATCH_CUST: $option.data('cust'),
                MATCH_MANUFACTURER: $option.data('manufacturer'),
                MATCH_MPN: $option.data('mpn')
            };

            // 更新bomData中的数据
            var itemData = bomData.find(function(item) {
                return item.Id === rowId;
            });
            if (itemData) {
                Object.assign(itemData, rowData);
            }

            // 获取当前选中项的完整数据
            var selectedOptions = optionsData[rowId];

            // 重新构建select的选项
            $select.empty();
            $select.append('<option value="' + data.value + '">' + data.value + '</option>');

            // 重新添加所有选项
            selectedOptions.forEach(function (item) {
                var displayText = item.MATCH_PARTNUMBER || 'NA';
                var detailInfo = [];

                if (item.MATCH_CUST && item.MATCH_CUST !== 'NA') {
                    detailInfo.push('客户: ' + item.MATCH_CUST);
                }
                if (item.MATCH_MANUFACTURER && item.MATCH_MANUFACTURER !== 'NA') {
                    detailInfo.push('制造商: ' + item.MATCH_MANUFACTURER);
                }
                if (item.MATCH_MPN && item.MATCH_MPN !== 'NA') {
                    detailInfo.push('MPN: ' + item.MATCH_MPN);
                }

                if (detailInfo.length > 0) {
                    displayText += ' (' + detailInfo.join(', ') + ')';
                }

                if (item.MATCH_PARTNUMBER !== data.value) {
                    $select.append(
                        $('<option>', {
                            value: item.MATCH_PARTNUMBER,
                            text: displayText,
                            'data-cust': item.MATCH_CUST || '',
                            'data-manufacturer': item.MATCH_MANUFACTURER || '',
                            'data-mpn': item.MATCH_MPN || ''
                        })
                    );
                }
            });

            // 更新卡片中的显示
            $card.find('.part-number').text(rowData.MATCH_PARTNUMBER);
            $card.find('[data-field="MATCH_CUST"]').text(rowData.MATCH_CUST === 'NA' ? '' : rowData.MATCH_CUST);
            $card.find('[data-field="MATCH_MANUFACTURER"]').text(rowData.MATCH_MANUFACTURER === 'NA' ? '' : rowData.MATCH_MANUFACTURER);
            $card.find('[data-field="MATCH_MPN"]').text(rowData.MATCH_MPN === 'NA' ? '' : rowData.MATCH_MPN);

            // 重新渲染表单
            form.render('select');
        });


        // 刷新物料组数据
        $(document).on('click', '#RefreshGroups', function () {
            var $btn = $(this);
            $btn.prop('disabled', true).text('刷新中...');

            loadMaterialGroups().then(function(groups) {
                initializeQuickFillSelect();
                layer.msg('物料组数据已刷新', {icon: 1});
            }).catch(function(error) {
                layer.msg('刷新失败: ' + error, {icon: 2});
            }).finally(function() {
                $btn.prop('disabled', false).text('刷新物料组');
            });
        });

        // 批量操作按钮事件
        $(document).on('click', '#selectAllBtn', function () {
            selectAllItems();
        });

        $(document).on('click', '#clearAllBtn', function () {
            clearAllItems();
        });

        // 修改一键填充物料组函数
        $(document).on('click', '#CopyGroup', function () {
            var selectedMaterialType = $('#MaterialType').val();
            if (!selectedMaterialType) {
                layer.msg('请先选择需要填充的物料组');
                return;
            }

            if (selectedItems.size === 0) {
                layer.msg('请至少选择一行数据');
                return;
            }

            var fillCount = 0;
            selectedItems.forEach(function(itemId) {
                var elemId = 'material-group-' + itemId;
                var instance = materialGroupInstances[elemId];
                if (instance) {
                    var targetValue = materialGroupsData.find(function(group) {
                        return group === selectedMaterialType;
                    });
                    if (targetValue) {
                        instance.setValue(targetValue);
                        fillCount++;
                    }
                }
            });

            layer.msg('物料组填充完成，共填充 ' + fillCount + ' 行数据', {icon: 1});
        });

        // 验证物料组值是否有效
        function isValidMaterialGroup(value) {
            return materialGroupsData.includes(value);
        }





        // 修改 validateSelectedMaterialGroups 函数
        function validateSelectedMaterialGroups() {
            if (selectedItems.size === 0) {
                return {
                    valid: false,
                    message: '请至少选择一条数据'
                };
            }

            var invalidRows = [];
            selectedItems.forEach(function(itemId) {
                var elemId = 'material-group-' + itemId;
                var instance = materialGroupInstances[elemId];
                var value = '';

                if (instance) {
                    var result = instance.getValue();
                    value = result.value || '';
                }

                if (!value || !materialGroupsData.includes(value)) {
                    var itemData = bomData.find(function(item) {
                        return item.Id === itemId;
                    });
                    invalidRows.push(itemData ? (itemData.MATCH_PARTNUMBER || itemId) : itemId);
                }
            });

            return {
                valid: invalidRows.length === 0,
                message: invalidRows.length > 0 ?
                    `请正确填写选中行的物料组（料号: ${invalidRows.join(', ')}）` : ''
            };
        }



        // 下一步按钮点击事件
        $('#nextbtn').on('click', function () {
            var validation = validateSelectedMaterialGroups();
            if (!validation.valid) {
                layer.msg(validation.message);
                return;
            }

            // 获取选中的数据
            var selectedData = [];
            selectedItems.forEach(function(itemId) {
                var itemData = bomData.find(function(item) {
                    return item.Id === itemId;
                });
                if (itemData) {
                    selectedData.push(itemData);
                }
            });

            // 验证MATCH_PARTNUMBER
            var emptyMatchRows = selectedData.filter(row => !row.MATCH_PARTNUMBER);
            if (emptyMatchRows.length > 0) {
                layer.msg('请为选中的所有行选择匹配料号');
                return;
            }

            // 收集选中行的详细数据
            var updateData = selectedData.map(function (row) {
                var materialGroup = '';
                var elemId = 'material-group-' + row.Id;
                var instance = materialGroupInstances[elemId];

                if (instance) {
                    var result = instance.getValue();
                    materialGroup = result.value || '';
                }

                return {
                    Id: row.Id,
                    MATERIAL_GROUP: materialGroup,
                    MATCH_PARTNUMBER: $(`select[data-id="${row.Id}"]`).val(),
                    MATCH_CUST: row.MATCH_CUST || '',
                    MATCH_MANUFACTURER: row.MATCH_MANUFACTURER || '',
                    MATCH_MPN: row.MATCH_MPN || ''
                };
            });

            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=UpdateStatus&RFQNo=' + RFQNo,
                type: 'post',
                data: {
                    updateData: JSON.stringify({
                        updateData
                    })
                },
                success: function (data) {
                    console.log(data);
                    var objdata = eval("(" + data + ")");
                    if (objdata.result == "success") {
                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                            navigateInIframe("../Flow/SendQuote.aspx?RFQNo=" + RFQNo, "发送报价-" + RFQNo);
                        });
                    } else {
                        layer.alert(objdata.msg);
                    }
                },
                error: function (data) {
                    layer.alert("发生错误，请稍后重试");
                }
            });
        });

        // 窗口大小变化时调整卡片容器高度
        $(window).on('resize', function() {
            var newHeight = Math.min(window.innerHeight - 300, 600);
            $('.cards-container').css('max-height', newHeight + 'px');
        });

        // 初始化页面
        loadMaterialGroups().then(function() {
            initializeQuickFillSelect();
            loadColumnAndBomData();
        }).catch(function(error) {
            layer.msg('加载物料组数据失败，将使用默认数据', {icon: 2});
            // 使用默认数据
            materialGroupsData = ['主动型', '被动型', '其他'];
            initializeQuickFillSelect();
            loadColumnAndBomData();
        });
    });
</script>

